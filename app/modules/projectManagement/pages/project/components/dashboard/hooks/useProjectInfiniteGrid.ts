import { use<PERSON>emo, useCallback, useState, useRef, useEffect } from "react";
import type { IData<PERSON>urce, IGetRowsParams, GridApi } from "ag-grid-community";
import { getProjectListApi } from "../../../redux/action/proDashAction";
import { STATUS_CODE } from "~/shared/constants";

// Define types locally to avoid module import issues
interface IProjectListParmas {
  start?: number;
  limit?: number;
  page: number;
  search?: string;
  filter?: any;
  order_by_name?: string;
  order_by_dir?: string;
}

interface IProjectData {
  id: number;
  project_id: string;
  project_name: string;
  customer_name?: string;
  project_status?: string;
  start_date?: string;
  end_date?: string;
  [key: string]: any;
}

interface IProjectListApiRes {
  success: boolean;
  message: string;
  data?: {
    data: IProjectData[];
  };
}

interface UseProjectInfiniteGridProps {
  search?: string;
  filter?: any;
  onDataChange?: (data: IProjectData[]) => void;
  cacheBlockSize?: number;
}

interface UseProjectInfiniteGridReturn {
  dataSource: IDatasource;
  loading: boolean;
  error: string | null;
  refreshGrid: (gridApi: GridApi) => void;
  clearError: () => void;
}

export const useProjectInfiniteGrid = ({
  search = "",
  filter,
  onDataChange,
  cacheBlockSize = 20,
}: UseProjectInfiniteGridProps): UseProjectInfiniteGridReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track ongoing requests to prevent race conditions
  const activeRequestRef = useRef<AbortController | null>(null);

  // Track requested ranges to prevent duplicate requests
  const requestedRangesRef = useRef<Set<string>>(new Set());

  // Simple cache for API responses
  const cacheRef = useRef<
    Map<string, { data: IProjectData[]; lastRow?: number }>
  >(new Map());

  // Optimized data source with proper error handling and caching
  const dataSource = useMemo<IDatasource>(
    () => ({
      rowCount: undefined, // Unknown row count for infinite scrolling

      getRows: async (params: IGetRowsParams) => {
        const { startRow, endRow, successCallback, failCallback } = params;

        // Create unique key for this request range
        const requestKey = `${startRow}-${endRow}`;

        try {
          // Check cache first
          const cacheKey = `${startRow}-${endRow}-${search}-${JSON.stringify(
            filter
          )}`;
          const cachedResult = cacheRef.current.get(cacheKey);

          if (cachedResult) {
            console.log(`Using cached data for range ${requestKey}`);
            successCallback(cachedResult.data, cachedResult.lastRow);
            return;
          }

          // Check if this exact range is already being requested
          if (requestedRangesRef.current.has(requestKey)) {
            console.log(
              `Request for range ${requestKey} already in progress, skipping...`
            );
            return;
          }

          // Add to requested ranges
          requestedRangesRef.current.add(requestKey);

          // Cancel any ongoing request for different range
          if (activeRequestRef.current) {
            activeRequestRef.current.abort();
          }

          // Create new abort controller for this request
          activeRequestRef.current = new AbortController();

          setLoading(true);
          setError(null);

          const tempFil: IProjectTempFil = {
            status: STATUS_CODE.ACTIVE,
            tab: "all",
          };
          if (filter?.tab) {
            tempFil.tab = filter.tab.toString() || "";
          }
          if (filter?.status !== undefined) {
            tempFil.status = filter.status;
          }
          if (filter?.project_contacts) {
            tempFil.project_contacts = filter.project_contacts;
          }
          if (filter?.sales_rep) {
            tempFil.sales_rep = filter.sales_rep;
          }
          if (filter?.project_manager) {
            tempFil.project_manager = filter.project_manager;
          }
          if (filter?.start_date) {
            tempFil.start_date = filter.start_date;
          }
          if (filter?.project_type) {
            tempFil.project_type = filter.project_type;
          }
          if (filter?.end_date) {
            tempFil.end_date = filter.end_date;
          }
          if (filter?.customer) {
            tempFil.customer = filter.customer;
          }
          if (filter?.customer_contact_id) {
            tempFil.customer_contact_id = filter.customer_contact_id;
          }
          if (filter?.project_status) {
            tempFil.project_status = filter.project_status;
          }
          if (filter?.status === STATUS_CODE.ALL) {
            delete tempFil.status;
          }

          const apiParams: IProjectListParmas = {
            start: startRow,
            limit: endRow - startRow,
            page: Math.floor(startRow / (endRow - startRow)) + 1,
            search: search || undefined,
            filter: tempFil || {},
            order_by_name: "",
            order_by_dir: "",
          };

          if (!apiParams.order_by_dir || apiParams.order_by_dir === "") {
            delete apiParams.order_by_dir; // Default to ascending order
          }
          if (!apiParams.order_by_name || apiParams.order_by_name === "") {
            delete apiParams.order_by_name; // Default to no specific ordering
          }
          // Call the project list API
          const response = (await getProjectListApi(
            apiParams
          )) as IProjectListApiRes;

          // Check if request was aborted
          if (activeRequestRef.current?.signal.aborted) {
            console.log("Request was aborted");
            return;
          }

          if (response?.success && response.data?.data) {
            const projectData = response.data.data;

            // Notify parent component of data change
            onDataChange?.(projectData);

            // Determine if this is the last page
            const lastRow =
              projectData.length < endRow - startRow
                ? startRow + projectData.length
                : undefined;

            // Cache the result
            cacheRef.current.set(cacheKey, { data: projectData, lastRow });

            // Success callback with data and last row info
            successCallback(projectData, lastRow);

            console.log(`Successfully loaded ${projectData.length} rows`);
          } else {
            // Handle API error response
            const errorMessage =
              response?.message || "Failed to fetch project data";
            setError(errorMessage);
            failCallback();
            console.error("API Error:", errorMessage);
          }
        } catch (error) {
          // Handle network or other errors
          const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";
          setError(errorMessage);
          failCallback();
          console.error("Network Error:", error);
        } finally {
          // Remove from requested ranges when done
          requestedRangesRef.current.delete(requestKey);
          setLoading(false);
        }
      },
    }),
    [search, filter, onDataChange, cacheBlockSize]
  );

  // Refresh grid function with timeout to avoid render cycle conflicts
  const refreshGrid = useCallback(
    (gridApi: GridApi) => {
      if (gridApi) {
        // Use setTimeout to avoid calling API during render cycle
        setTimeout(() => {
          try {
            gridApi.setDatasource(dataSource);
          } catch (error) {
            console.error("Error setting datasource:", error);
          }
        }, 0);
      }
    },
    [dataSource]
  );

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Cleanup function to cancel ongoing requests and clear cache
  useEffect(() => {
    return () => {
      if (activeRequestRef.current) {
        activeRequestRef.current.abort();
      }
      // Clear requested ranges and cache on unmount
      requestedRangesRef.current.clear();
      cacheRef.current.clear();
    };
  }, []);

  // Clear requested ranges and cache when search or filter changes
  useEffect(() => {
    requestedRangesRef.current.clear();
    cacheRef.current.clear();
  }, [search, filter]);

  return {
    dataSource,
    loading,
    error,
    refreshGrid,
    clearError,
  };
};

export type { IProjectData, IProjectListParmas, IProjectListApiRes };
