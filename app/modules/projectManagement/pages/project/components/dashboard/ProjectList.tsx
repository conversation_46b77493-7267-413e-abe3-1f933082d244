"use client";
import React, {
  useCallback,
  useMemo,
  useState,
  useRef,
  useEffect,
} from "react";
import type { ColDef, GridReadyEvent, GridApi } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import {
  useProjectInfiniteGrid,
  type IProjectData,
} from "./hooks/useProjectInfiniteGrid";
import { getGModuleFilters } from "~/zustand";

// Loading cell renderer component
const LoadingCellRenderer = () => (
  <div className="flex items-center justify-center">
    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
  </div>
);

// Error cell renderer component
const ErrorCellRenderer = () => (
  <div className="text-red-500 text-sm">Failed to load</div>
);

interface ProjectListProps {
  search?: string;
  onDataChange?: (data: IProjectData[]) => void;
}
const ProjectList: React.FC<ProjectListProps> = ({
  search = "",
  onDataChange,
}) => {
  const gridRef = useRef<AgGridReact>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);

  const filterSrv: Partial<ProjectFilter> | undefined = getGModuleFilters() as
    | Partial<ProjectFilter>
    | undefined;
  const defaulProjectStatus = useMemo(() => {
    if (filterSrv?.tab === "open") {
      return "bidding,project_submittal,started,unscheduled,pending ";
    } else if (filterSrv?.tab === "completed") {
      return "completed";
    } else {
      return "";
    }
  }, [filterSrv?.tab]);

  // Memoize filter object to avoid unnecessary re-renders
  // This filter will be used in the custom hook to fetch data
  const filter = useMemo(() => {
    return {
      start_date: filterSrv?.start_date || "",
      end_date: filterSrv?.end_date || "",
      status: filterSrv?.status ?? "0",
      customer: filterSrv?.customer || "",
      customer_contact_id: filterSrv?.customer_contact_id || "",
      project_contacts: filterSrv?.project_contacts || "",
      project_manager: filterSrv?.project_manager || "",
      project_status: filterSrv?.project_status || defaulProjectStatus,
      project_type: filterSrv?.project_type || "",
      sales_rep: filterSrv?.sales_rep || "",
      tab: filterSrv?.tab || "all",
    };
  }, [filterSrv]);
  // Use the custom hook for infinite grid functionality
  const { dataSource, loading, error, refreshGrid, clearError } =
    useProjectInfiniteGrid({
      search,
      filter,
      onDataChange,
      cacheBlockSize: 20,
    });

  // Column definitions optimized for project data
  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "Row",
        maxWidth: 80,
        valueGetter: "node.rowIndex + 1",
        cellRenderer: (params: any) => {
          if (params.value !== undefined) {
            return params.value;
          }
          return <LoadingCellRenderer />;
        },
        pinned: "left",
        sortable: false,
        filter: false,
      },
      {
        field: "project_name",
        headerName: "Project Name",
        minWidth: 200,
        flex: 2,
        cellRenderer: (params: any) => {
          if (!params.data) return <LoadingCellRenderer />;
          if (params.data.error) return <ErrorCellRenderer />;
          return params.value || "N/A";
        },
      },
      {
        field: "project_id",
        headerName: "Project ID",
        minWidth: 120,
        flex: 1,
      },
      {
        field: "customer_name",
        headerName: "Customer",
        minWidth: 150,
        flex: 1,
      },
      {
        field: "project_status",
        headerName: "Status",
        minWidth: 120,
        flex: 1,
      },
      {
        field: "start_date",
        headerName: "Start Date",
        minWidth: 120,
        flex: 1,
        valueFormatter: (params) => {
          if (!params.value) return "N/A";
          return new Date(params.value).toLocaleDateString();
        },
      },
      {
        field: "end_date",
        headerName: "End Date",
        minWidth: 120,
        flex: 1,
        valueFormatter: (params) => {
          if (!params.value) return "N/A";
          return new Date(params.value).toLocaleDateString();
        },
      },
    ],
    []
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      resizable: true,
      suppressMenu: false,
    }),
    []
  );

  // Grid ready handler with proper timing
  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      setGridApi(params.api);
      // Use setTimeout to avoid calling API during render cycle
      setTimeout(() => {
        try {
          params.api.setDatasource(dataSource);
        } catch (error) {
          console.error("Error setting initial datasource:", error);
        }
      }, 0);
    },
    [dataSource]
  );

  // Refresh grid when search or filter changes
  useEffect(() => {
    if (gridApi) {
      // Debounce the refresh to avoid too many API calls
      const timeoutId = setTimeout(() => {
        refreshGrid(gridApi);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [gridApi, refreshGrid]);

  return (
    <div className="w-full h-full">
      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium">Error loading projects</h3>
              <div className="mt-2 text-sm">
                <p>{error}</p>
              </div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="inline-flex text-red-400 hover:text-red-600 focus:outline-none focus:text-red-600"
                aria-label="Dismiss error"
              >
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* AgGrid Table */}
      <div className="ag-theme-alpine" style={{ height: 600, width: "100%" }}>
        <div
          className={`list-view-table ag-grid-cell-pointer ag-theme-alpine header-custom-height-remove ${"h-[calc(100dvh-238px)]"}`}
        >
          <AgGridReact
            ref={gridRef}
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            rowModelType="infinite"
            cacheBlockSize={20} // Optimized page size
            maxConcurrentDatasourceRequests={1} // Reduced to 1 to prevent race conditions
            maxBlocksInCache={3} // Minimal cache to reduce initial requests
            rowBuffer={0} // No buffer to prevent extra requests
            cacheOverflowSize={1} // Minimal overflow
            infiniteInitialRowCount={20} // Set initial count to prevent multiple requests
            onGridReady={onGridReady}
            suppressRowClickSelection={true}
            // rowSelection="single"
            animateRows={false} // Disable animations to prevent render issues
            // enableCellTextSelection={true}
            suppressCellFocus={true}
            // Performance optimizations
            suppressColumnVirtualisation={false}
            suppressRowVirtualisation={false}
            // Prevent render cycle issues
            suppressScrollOnNewData={true}
            suppressAnimationFrame={true}
            // Loading overlay
            loadingOverlayComponent={() => (
              <div className="flex items-center justify-center h-full">
                <LoadingCellRenderer />
                <span className="ml-2">Loading projects...</span>
              </div>
            )}
            // No rows overlay
            noRowsOverlayComponent={() => (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <svg
                  className="w-12 h-12 mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <p className="text-lg font-medium">No projects found</p>
                <p className="text-sm">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default ProjectList;
